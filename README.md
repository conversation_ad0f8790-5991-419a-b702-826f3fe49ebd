# Movies App

This is a React Native app built with Expo, styled like Netflix and OSN, using the TMDB API for movie data. The app features beautiful colors, modern UI, and smooth animations. No database is required.

## How to Run

- To start the app on Android: `npm run android`
- To start the app on iOS (Mac only): `npm run ios`
- To start the app on Web: `npm run web`

## Features

- Browse movies from TMDB
- Modern, animated UI inspired by Netflix and OSN
- No backend database required

## Customization

- Update styles and colors in the components for your own look
- Replace placeholder assets with your own images

## Next Steps

- Add your TMDB API key in the code
- Start building your screens and components
